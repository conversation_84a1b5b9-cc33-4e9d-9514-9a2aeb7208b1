import { AxiosRequestConfig } from "axios";
import { BaseObject } from "src/app/global";
import { CompensationComponent } from "src/modules/Employees/Compensation/Compensation";
import {
  createPseudoLinkAndDownload,
  getFilenameFromContentDisposition,
  ValidFileExtensions,
} from "src/utils/fileUtils";
import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import {
  CompensationTemplateDetailRequest,
  CreatePayrollComponent,
  ExcludedEmployeesPayruns,
  PayRunData,
  PayRunDetail,
  PayrollComponentV2,
  PayrollTemplate,
  PayrollTemplateV2,
  Payrun,
  PayrunApprovals,
  PayrunEmployeeCompensationDetail,
  PayrunHistory,
  StatutoryComponent,
  StatutoryComponentRequest,
  UpdateEmployeeDetails,
} from "./api_definitions/payroll.service";
import fileuploaderService from "./fileuploader.service";

class PayrollService {
  getAllTemplates = async () => {
    const resp = await httpClient<BaseResponse<PayrollTemplate[]>>(apiRegister.PAYROLL.paths["get-payroll-templates"]);
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  getTemplateDetails = async ({
    business_unit,
    department,
    work_role,
    job_title,
    employee_type,
    country,
  }: CompensationTemplateDetailRequest) => {
    const resp = await httpClient<BaseResponse<PayrollTemplateV2[]>>(
      apiRegister.PAYROLL.paths["compensation-template-details"],
      {
        params: {
          business_unit,
          department,
          work_role,
          job_title,
          employee_type,
          country,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      return null;
    }
    return resp?.data?.response;
  };

  getAllTemplatesV2 = async () => {
    const resp = await httpClient<BaseResponse<PayrollTemplateV2[]>>(
      apiRegister.PAYROLL.paths["get-payroll-templates"],
    );
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  createTemplate = async (template: PayrollTemplateV2) => {
    const resp = await httpClient<BaseResponse<CreatePayrollComponent>>(
      apiRegister.PAYROLL.paths["add-payroll-template"],
      {
        method: "POST",
        data: template,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating template");
    }
    return resp?.data?.response;
  };

  updateTemplate = async (template: PayrollTemplateV2) => {
    const resp = await httpClient<BaseResponse<CreatePayrollComponent>>(
      apiRegister.PAYROLL.paths["update-payroll-template"],
      {
        method: "PATCH",
        data: template,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating template");
    }
    return resp?.data?.response;
  };

  deleteTemplate = async (templateId: string) => {
    const resp = await httpClient<BaseResponse<CreatePayrollComponent>>(
      apiRegister.PAYROLL.paths["delete-payroll-template"],
      {
        method: "DELETE",
        data: {
          name: templateId,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error deleting template");
    }
    return resp?.data?.response;
  };

  getAllCompensationComponents = async (queryParams: {
    country: string;
    statutory?: boolean;
    active?: boolean;
    employee_types?: string;
  }) => {
    console.log({ queryParams });
    const resp = await httpClient<BaseResponse<CompensationComponent[]>>(
      apiRegister.PAYROLL.paths["all-payroll-components"],
      {
        params: {
          ...queryParams,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching all compensation components");
    }
    return resp?.data?.response || [];
  };

  softDelete = async (mode: "activate" | "deactivate", name: string) => {
    const resp = await httpClient<BaseResponse<string>>(
      apiRegister.PAYROLL.paths["soft-delete"].replace(":mode", mode),
      {
        method: "PUT",
        data: {
          name,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error soft deleting template");
    }
    return resp?.data?.response;
  };

  computePreview = async (components: PayrollComponentV2[], previewValue: number, isCtc: boolean) => {
    const resp = await httpClient<BaseResponse<{ components: Record<string, number> }>>(
      apiRegister.PAYROLL.paths["compute-preview"],
      {
        method: "POST",
        params: {
          ctc: isCtc ? previewValue : undefined,
          gross: !isCtc ? previewValue : undefined,
        },
        data: components,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error computing preview");
    }
    return resp?.data?.response;
  };

  createCompensationComponent = async (component: Partial<CompensationComponent>) => {
    const resp = await httpClient<BaseResponse<CompensationComponent>>(
      apiRegister.PAYROLL.paths["create-compensation-component"],
      {
        method: "POST",
        data: component,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating compensation component");
    }
    return resp?.data?.response;
  };

  getStatutoryComponents = async (taxType: string) => {
    const resp = await httpClient<BaseResponse<StatutoryComponent[]>>(
      apiRegister.PAYROLL.paths["get-statutory-components"],
      {
        method: "GET",
        params: { tax_type: taxType },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching statutory components");
    }
    return resp?.data?.response;
  };

  deleteCompensationComponent = async (id: string) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.PAYROLL.paths["delete-compensation-component"], {
      method: "DELETE",
      data: {
        id,
      },
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error deleting compensation component");
    }
    return resp?.data?.response;
  };

  createStatutoryComponent = async (component: StatutoryComponentRequest) => {
    const resp = await httpClient<BaseResponse<StatutoryComponent>>(
      apiRegister.PAYROLL.paths["create-statutory-component"],
      {
        method: "POST",
        data: component,
      },
    );
    if (resp?.status !== 200 || resp?.data?.errors?.length > 0) {
      throw new Error("Error creating statutory component");
    }
    return resp?.data;
  };

  updateStatutoryComponent = async (component: StatutoryComponentRequest) => {
    const resp = await httpClient<BaseResponse<StatutoryComponent>>(
      apiRegister.PAYROLL.paths["update-statutory-component"],
      {
        method: "PATCH",
        data: component,
      },
    );
    if (resp?.status !== 200 || resp?.data?.errors?.length > 0) {
      throw new Error("Error updating statutory component");
    }
    return resp?.data;
  };

  updateCompensationComponent = async (component: Partial<CompensationComponent>) => {
    const resp = await httpClient<BaseResponse<CompensationComponent>>(
      apiRegister.PAYROLL.paths["update-compensation-component"],
      {
        method: "PATCH",
        data: component,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating compensation component");
    }
    return resp?.data?.response;
  };

  changeCompensationComponentStatus = async (id: string, active: boolean) => {
    const resp = await httpClient<BaseResponse<CompensationComponent>>(
      apiRegister.PAYROLL.paths["change-compensation-component-status"].replace(
        ":status",
        active ? "activate" : "deactivate",
      ),
      {
        method: "PUT",
        data: {
          id,
          active,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error changing compensation component status");
    }
    return resp?.data?.response;
  };

  uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    const resp = await fileuploaderService.uploadFile(
      apiRegister.PAYROLL.paths["upload-payroll-details"],
      formData,
      undefined,
      undefined,
      true,
    );
    if (resp !== undefined && resp.type === "error") throw resp;
    else return resp;
  };

  downloadSampleImportPayrollTemplate = async () => {
    const resp = await httpClient<Blob>(apiRegister.PAYROLL.paths["download-sample-template"], {
      responseType: "blob",
    });

    const [fileName, extention] = (
      getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
    ).split(".");

    if (fileName && extention) {
      createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
    }

    return [];
  };

  exportPayroll = async (payload?: BaseObject) => {
    const isPayloadEmpty = Object.values(payload || {}).every((val) => !val);
    const requestPayload = isPayloadEmpty
      ? undefined
      : Object.keys(payload || {}).reduce((acc, key) => {
          if (key && payload?.[key]) {
            acc = {
              ...acc,
              [key]: payload[key],
            };
          }
          return acc;
        }, {});

    try {
      const endpoint = apiRegister.PAYROLL.paths["export-payroll"];
      const resp = await httpClient<Blob>(endpoint, {
        method: "GET",
        responseType: "blob",
        params: !isPayloadEmpty ? { ...requestPayload } : undefined,
      });
      const [fileName, extention] = (
        getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
      ).split(".");

      if (fileName && extention) {
        createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
      }
      return [];
    } catch (_error) {
      return null;
    }
  };

  getPayruns = async () => {
    const resp = await httpClient<BaseResponse<Payrun>>(apiRegister.PAYROLL.paths["get-payruns"]);
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching payruns");
    }
    return resp?.data?.response;
  };
  getPayScheduleData = async () => {
    const resp = await httpClient<BaseResponse<PayRunData[]>>(apiRegister.PAYROLL.paths["get-pay-schedule-data"]);
    return resp?.data?.response;
  };

  createPaySchedule = async (payload: PayRunData) => {
    const resp = await httpClient<BaseResponse<PayRunData>>(apiRegister.PAYROLL.paths["create-pay-schedule"], {
      method: "POST",
      data: payload,
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating pay schedule");
    }
    return resp?.data?.response;
  };

  updatePaySchedule = async (payload: PayRunData) => {
    const resp = await httpClient<BaseResponse<PayRunData>>(apiRegister.PAYROLL.paths["update-pay-schedule"], {
      method: "PATCH",
      data: payload,
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating pay schedule");
    }
    return resp?.data?.response;
  };

  getPayrunHistory = async () => {
    const resp = await httpClient<BaseResponse<PayrunHistory[]>>(apiRegister.PAYROLL.paths["get-payrun-history"]);
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching payrun history");
    }
    return resp?.data?.response;
  };

  getPayrunById = async (payrunId?: string, payrunType?: string) => {
    if (!payrunId) {
      return null;
    }
    const resp = await httpClient<BaseResponse<PayRunDetail>>(
      apiRegister.PAYROLL.paths[payrunType !== "Regular" ? "get-adhoc-payrun-by-id" : "get-payrun_by_id"].replace(
        ":payrunId",
        payrunId,
      ),
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching payrun history");
    }
    return resp?.data?.response;
  };

  createPayRun = async (name: string) => {
    const resp = await httpClient<BaseResponse<PayRunData>>(apiRegister.PAYROLL.paths["create-pay-run"], {
      method: "POST",
      data: { pay_schedule_name: name },
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating pay run");
    }
    return resp?.data?.response;
  };

  deletePayRun = async (payrunId: string, payrunType: string) => {
    const resp = await httpClient<BaseResponse<string>>(
      apiRegister.PAYROLL.paths["delete-pay-run"].replace(":payrunType", payrunType).replace(":payrunId", payrunId),
      {
        method: "DELETE",
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error deleting pay run");
    }
    return resp?.data?.response;
  };

  skipPayRun = async (payrunIds: string[], reason: string, payrunType: string) => {
    const resp = await httpClient<BaseResponse<string>>(
      apiRegister.PAYROLL.paths["skip-pay-run"].replace(":payrunType", payrunType),
      {
        method: "PUT",
        data: {
          pay_run_ids: payrunIds,
          comments: reason,
        },
      },
    );
    return resp?.data?.response;
  };

  markPayRunAsPaid = async (payrunIds: string[], payment_mode: string, payment_date: string, payrunType: string) => {
    const resp = await httpClient<BaseResponse<string>>(
      apiRegister.PAYROLL.paths["mark-paid-pay-run"].replace(":payrunType", payrunType),
      {
        method: "PUT",
        data: {
          pay_run_ids: payrunIds,
          payment_mode,
          payment_date,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error marking pay run as paid");
    }
    return resp?.data?.response;
  };

  getEmployeeDetailsPayrunId = async (employeePayrunId: string) => {
    if (!employeePayrunId) {
      return null;
    }
    const resp = await httpClient<BaseResponse<PayrunEmployeeCompensationDetail>>(
      apiRegister.PAYROLL.paths["get-employee-payrun-by-id"].replace(":employeePayRunId", employeePayrunId),
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching payrun history");
    }
    return resp?.data?.response;
  };

  activePaySchedule = async (name: string, isActive: boolean) => {
    const activateText = isActive ? "activate" : "deactivate";
    const endpoint = apiRegister.PAYROLL.paths["active-pay-schedule"]
      .replace(":name", name)
      .replace(":status", activateText);
    const resp = await httpClient<BaseResponse<PayRunData>>(endpoint, {
      method: "PUT",
      data: {},
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error activating pay run");
    }
    return resp?.data?.response;
  };

  updateEmployeeDetails = async (payload: UpdateEmployeeDetails) => {
    const resp = await httpClient<BaseResponse<PayrunEmployeeCompensationDetail[]>>(
      apiRegister.PAYROLL.paths["update-employee-payrun"],
      {
        method: "PATCH",
        data: payload,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching payrun history");
    }
    return resp?.data?.response;
  };

  deletePaySchedule = async (name: string) => {
    const resp = await httpClient<BaseResponse<PayRunData>>(
      apiRegister.PAYROLL.paths["delete-pay-schedule"].replace(":name", name),
      {
        method: "DELETE",
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error deleting pay schedule");
    }
    return resp?.data?.response;
  };

  excludeEmployeesFromPayrun = async (employeeIds: string[], reason: string) => {
    const resp = await httpClient<BaseResponse<PayrunEmployeeCompensationDetail[]>>(
      apiRegister.PAYROLL.paths["exclude-employees-from-payrun"],
      {
        method: "PATCH",
        data: {
          employee_pay_run_ids: employeeIds,
          comments: reason,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error excluding employees from payrun");
    }
    return resp?.data?.response;
  };

  includeEmployeesInPayrun = async (employeeIds: string[]) => {
    const resp = await httpClient<BaseResponse<PayrunEmployeeCompensationDetail[]>>(
      apiRegister.PAYROLL.paths["include-employees-in-payrun"],
      {
        method: "PATCH",
        data: employeeIds,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error including employees in payrun");
    }
    return resp?.data?.response;
  };
  refreshEmployeePayrunData = async (employeeIds: string[]) => {
    const resp = await httpClient<BaseResponse<PayrunEmployeeCompensationDetail[]>>(
      apiRegister.PAYROLL.paths["refresh-employee-payrun-data"],
      {
        method: "PUT",
        data: employeeIds,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error refreshing employee payrun data");
    }
    return resp?.data?.response;
  };

  getExcludedPayments = async () => {
    const resp = await httpClient<BaseResponse<ExcludedEmployeesPayruns>>(
      apiRegister.PAYROLL.paths["get-excluded-payments"],
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching excluded payments");
    }
    return resp?.data?.response;
  };

  updateEmployeeDetailsInBulk = async (payload: UpdateEmployeeDetails) => {
    const resp = await httpClient<BaseResponse<PayrunEmployeeCompensationDetail[]>>(
      apiRegister.PAYROLL.paths["update-employee-payrun-bulk"],
      {
        method: "PATCH",
        data: payload,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching payrun history");
    }
    return resp?.data?.response;
  };

  updatePayrun = async (payrunType: "Off Cycle" | "Regular", employeeIds: string[]) => {
    const resp = await httpClient<BaseResponse<PayRunData>>(
      apiRegister.PAYROLL.paths["update-payrun"].replace(":payrunType", payrunType),
      {
        method: "POST",
        data: {
          employee_pay_run_ids: employeeIds,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating payrun");
    }
    return resp?.data?.response;
  };

  submitPayrun = async (payrunIds: string[], payrunType: string) => {
    const resp = await httpClient<BaseResponse<PayRunData>>(
      apiRegister.PAYROLL.paths["submit-payrun"].replace(":payrunType", payrunType),
      {
        method: "PUT",
        data: payrunIds,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error submitting payrun");
    }
    return resp?.data?.response;
  };

  getPayrunApprovals = async () => {
    const resp = await httpClient<BaseResponse<PayrunApprovals[]>>(apiRegister.PAYROLL.paths["get-payrun-approvals"]);
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching payrun approvals");
    }
    return resp?.data?.response;
  };

  approvePayrun = async (payrunIds: string[], reason?: string, payrunType?: string) => {
    const resp = await httpClient<BaseResponse<PayRunData>>(
      apiRegister.PAYROLL.paths["approve-payrun"].replace(":payrunType", payrunType || ""),
      {
        method: "PUT",
        data: {
          pay_run_ids: payrunIds,
          comments: reason,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error approving payrun");
    }
    return resp?.data?.response;
  };

  rejectPayrun = async (payrunIds: string[], reason: string, payrunType: string) => {
    const resp = await httpClient<BaseResponse<PayRunData>>(
      apiRegister.PAYROLL.paths["reject-payrun"].replace(":payrunType", payrunType),
      {
        method: "PUT",
        data: {
          pay_run_ids: payrunIds,
          comments: reason,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error rejecting payrun");
    }
    return resp?.data?.response;
  };

  getAdhocPayruns = async () => {
    const resp = await httpClient<BaseResponse<Payrun[]>>(apiRegister.PAYROLL.paths["get-adhoc-payruns"]);
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching ad hoc payruns");
    }
    return resp?.data?.response;
  };

  getAdHocPayrunApprovals = async () => {
    const resp = await httpClient<BaseResponse<PayrunApprovals[]>>(
      apiRegister.PAYROLL.paths["get-adhoc-payrun-approvals"],
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching payrun approvals");
    }
    return resp?.data?.response;
  };

  exportPayrunDetails = async (payrunType: string, payrunId: string, options?: AxiosRequestConfig) => {
    const resp = await httpClient<Blob>(
      apiRegister.PAYROLL.paths["export-payrun-details"].replace(":payrunType", payrunType),
      {
        responseType: "blob",
        params: {
          pay_run_id: payrunId,
        },
        ...options,
      },
    );
    const [fileName, extention] = (
      getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
    ).split(".");

    if (fileName && extention) {
      createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
    }

    return [];
  };

  downloadBulkPayslips = async (payrunId: string, payrunType: string) => {
    const resp = await httpClient<Blob>(apiRegister.PAYROLL.paths["download-bulk-payslips"], {
      responseType: "blob",
      params: {
        pay_run_id: payrunId,
        pay_run_type: payrunType,
      },
    });
    const [fileName, extention] = (
      getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
    ).split(".");

    if (fileName && extention) {
      createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
    }

    return [];
  };

  downloadEmployeePayrunPayslip = async (employeePayrunId: string) => {
    const resp = await httpClient<Blob>(apiRegister.PAYROLL.paths["download-employee-payrun-payslip"], {
      responseType: "blob",
      params: {
        employee_pay_run_id: employeePayrunId,
      },
    });
    const [fileName, extention] = (
      getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
    ).split(".");

    if (fileName && extention) {
      createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
    }

    return [];
  };
}

export default new PayrollService();
