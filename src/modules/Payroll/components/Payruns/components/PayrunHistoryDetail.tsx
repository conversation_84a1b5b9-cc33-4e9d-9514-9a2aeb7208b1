import { <PERSON>, Card, CardContent, Chip, Divider, Grid, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiMask from "src/modules/Common/EffiViews/components/EffiMask";
import DataTable from "src/modules/Common/Table/DataTable";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayrunHistory } from "src/services/api_definitions/payroll.service.d";
import payrollService from "src/services/payroll.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";

interface PayrunHistoryDetailProps {
  selectedPayrun: PayrunHistory;
  onBack: () => void;
}

const PayrunHistoryDetail: React.FC<PayrunHistoryDetailProps> = ({ selectedPayrun, onBack }) => {
  const { data: payrunDetails, isLoading } = useQuery(
    ["get-payrun-by-id", selectedPayrun.pay_run_id],
    async () => payrollService.getPayrunById(selectedPayrun.pay_run_id, selectedPayrun.pay_run_type),
    {
      enabled: !!selectedPayrun.pay_run_id,
    },
  );

  const getTitle = () => {
    return `${selectedPayrun.pay_run_type} Pay Run (${selectedPayrun.period})`;
  };

  const getSubtitle = () => {
    return `${selectedPayrun.pay_run_id}`;
  };

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <ContentHeader
        showBackButton
        goBack={onBack}
        title={getTitle()}
        subtitle={getSubtitle()}
        rightContent={
          <Chip
            label={selectedPayrun.status}
            sx={{
              color: getStatusColors(selectedPayrun.status),
              backgroundColor: `${getStatusColors(selectedPayrun.status)}20`,
              fontWeight: 600,
            }}
          />
        }
      />
      <Divider />

      {/* Pay Run Details Section */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Pay Run Details
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Pay Schedule Name
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {payrunDetails?.pay_schedule_name || "-"}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Pay Run Type
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {selectedPayrun.pay_run_type}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                No of employees
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {payrunDetails?.active_employees || "-"}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Approver
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {payrunDetails?.approver || "-"}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Date of Approval
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {payrunDetails?.approval_date ? formatDateToDayMonthYear(payrunDetails.approval_date) : "-"}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Pay Day
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {selectedPayrun.payment_date ? formatDateToDayMonthYear(selectedPayrun.payment_date) : "-"}
              </Typography>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Payroll Cost
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                <EffiMask isMasked={false}>{formatCurrency(payrunDetails?.total_gross_pay)}</EffiMask>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Earnings
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                <EffiMask isMasked={false}>{formatCurrency(payrunDetails?.total_earnings)}</EffiMask>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Employees Net Pay
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                <EffiMask isMasked={false}>{formatCurrency(selectedPayrun.total_net_pay)}</EffiMask>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Statutory Deductions
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                <EffiMask isMasked={false}>{formatCurrency(payrunDetails?.total_statutory_deductions)}</EffiMask>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Total TDS
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                <EffiMask isMasked={false}>{formatCurrency(payrunDetails?.total_tds)}</EffiMask>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="body2" color="text.secondary">
                Mode of Payment
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {selectedPayrun.payment_mode || "-"}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Employee Details Section */}
      <Box>
        <Typography variant="h6" gutterBottom>
          Employee Details
        </Typography>
        <DataTable
          data={payrunDetails?.employee_details || []}
          enableFilters
          enableGlobalFilterModes={false}
          columns={[
            {
              accessorKey: "employee_name",
              header: "Employee",
              size: 200,
              Cell: ({ row }) => (
                <Typography variant="body2" color="primary" sx={{ cursor: "pointer" }}>
                  {row.original.employee_name || row.original.name}
                </Typography>
              ),
            },
            {
              accessorKey: "paid_days",
              header: "Paid Days",
              size: 120,
              Cell: ({ row }) => row.original.paid_days || "-",
            },
            {
              accessorKey: "gross_pay",
              header: "Gross Pay",
              size: 150,
              Cell: ({ row }) => (
                <EffiMask isMasked={false}>{formatCurrency(row.original.gross_pay)}</EffiMask>
              ),
            },
            {
              accessorKey: "deductions",
              header: "Deductions",
              size: 150,
              Cell: ({ row }) => (
                <EffiMask isMasked={false}>{formatCurrency(row.original.total_deductions)}</EffiMask>
              ),
            },
            {
              accessorKey: "tds",
              header: "TDS",
              size: 120,
              Cell: ({ row }) => (
                <EffiMask isMasked={false}>{formatCurrency(row.original.tds)}</EffiMask>
              ),
            },
            {
              accessorKey: "net_amount",
              header: "Net Amount",
              size: 150,
              Cell: ({ row }) => (
                <EffiMask isMasked={false}>{formatCurrency(row.original.net_pay)}</EffiMask>
              ),
            },
          ]}
          state={{
            showSkeletons: isLoading,
            showColumnFilters: true,
          }}
        />
      </Box>
    </Box>
  );
};

export default PayrunHistoryDetail;
