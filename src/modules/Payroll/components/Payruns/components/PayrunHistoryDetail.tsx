import { DownloadForOfflineOutlined, FilterListOutlined, SearchOutlined } from "@mui/icons-material";
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Divider,
  Grid,
  Grid2,
  IconButton,
  InputAdornment,
  Paper,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiMask from "src/modules/Common/EffiViews/components/EffiMask";
import DataTable from "src/modules/Common/Table/DataTable";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayrunHistory } from "src/services/api_definitions/payroll.service.d";
import payrollService from "src/services/payroll.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";

interface PayrunHistoryDetailProps {
  selectedPayrun: PayrunHistory;
  onBack: () => void;
}

const PayrunHistoryDetail: React.FC<PayrunHistoryDetailProps> = ({ selectedPayrun, onBack }) => {
  const { data: payrunDetails, isLoading } = useQuery(
    ["get-payrun-by-id", selectedPayrun.pay_run_id],
    async () => payrollService.getPayrunById(selectedPayrun.pay_run_id, selectedPayrun.pay_run_type),
    {
      enabled: !!selectedPayrun.pay_run_id,
    },
  );

  const downloadBulkPayslipsMutation = useMutation({
    mutationKey: ["download-bulk-payslips"],
    mutationFn: async () => payrollService.downloadBulkPayslips(selectedPayrun.pay_run_id, selectedPayrun.pay_run_type),
  });

  const downloadEmployeePayslipMutation = useMutation({
    mutationKey: ["download-employee-payslip"],
    mutationFn: async (employeePayrunId: string) => payrollService.downloadEmployeePayrunPayslip(employeePayrunId),
  });

  const getTitle = () => {
    return `${selectedPayrun.pay_run_type} Pay Run (${selectedPayrun.period})`;
  };

  const getSubtitle = () => {
    return `${selectedPayrun.pay_run_id}`;
  };

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <ContentHeader showBackButton goBack={onBack} title={getTitle()} subtitle={getSubtitle()} />
        <Box display="flex" alignItems="center" gap={2}>
          <TextField
            size="small"
            placeholder="Search"
            sx={{ width: 300 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchOutlined fontSize="small" />
                </InputAdornment>
              ),
            }}
          />
          <IconButton>
            <FilterListOutlined />
          </IconButton>
          <Tooltip title="Download Bulk Payslips">
            <IconButton
              color="primary"
              onClick={() => downloadBulkPayslipsMutation.mutate()}
              disabled={downloadBulkPayslipsMutation.isLoading}
              sx={{
                backgroundColor: "primary.main",
                color: "white",
                "&:hover": {
                  backgroundColor: "primary.dark",
                },
                "&:disabled": {
                  backgroundColor: "grey.300",
                  color: "grey.500",
                },
              }}
            >
              <DownloadForOfflineOutlined />
            </IconButton>
          </Tooltip>
          <Chip
            label={selectedPayrun.status}
            sx={{
              color: getStatusColors(selectedPayrun.status),
              backgroundColor: `${getStatusColors(selectedPayrun.status)}20`,
              fontWeight: 600,
            }}
          />
        </Box>
      </Box>
      <Divider />

      {/* Pay Run Details Section */}
      <Box display="flex" flexDirection="column" gap={1}>
        <Typography variant="body2" fontWeight={600} gutterBottom>
          Pay Run Details
        </Typography>

        {/* First Section - Basic Details */}
        <Grid2 component={Paper} elevation={2} container spacing={3} padding={2} margin={[0, 1]}>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Pay Schedule Name
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              {payrunDetails?.pay_schedule_name || "-"}
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Pay Run Type
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              {selectedPayrun.pay_run_type}
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              No of employees
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              {payrunDetails?.active_employees || "-"}
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Approver
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              {payrunDetails?.progress_timeline?.find((item) => item.actioned_by)?.actioned_by || "-"}
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Date of Approval
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              {(() => {
                const actionedItem = payrunDetails?.progress_timeline?.find(
                  (item) => item.actioned_at && item.actioned_at !== null,
                );
                return actionedItem?.actioned_at ? formatDateToDayMonthYear(actionedItem.actioned_at) : "-";
              })()}
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Pay Day
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              {selectedPayrun.payment_date ? formatDateToDayMonthYear(selectedPayrun.payment_date) : "-"}
            </Typography>
          </Grid2>
        </Grid2>
        {/* Second Section - Financial Details */}
        <Grid2 component={Paper} elevation={2} container spacing={3} padding={2} margin={[0, 1]}>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Payroll Cost
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              <EffiMask isMasked={false}>{formatCurrency(payrunDetails?.total_gross_pay || 0)}</EffiMask>
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Earnings
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              <EffiMask isMasked={false}>{formatCurrency(payrunDetails?.total_earnings || 0)}</EffiMask>
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Employees Net Pay
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              <EffiMask isMasked={false}>{formatCurrency(selectedPayrun.total_net_pay)}</EffiMask>
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Statutory Deductions
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              <EffiMask isMasked={false}>{formatCurrency(payrunDetails?.total_statutory_deductions || 0)}</EffiMask>
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Total TDS
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              <EffiMask isMasked={false}>{formatCurrency(payrunDetails?.total_taxes || 0)}</EffiMask>
            </Typography>
          </Grid2>
          <Grid2 size={2}>
            <Typography variant="body2" color="text.secondary">
              Mode of Payment
            </Typography>
            <Typography variant="body1" fontWeight={500}>
              {selectedPayrun.payment_mode || "-"}
            </Typography>
          </Grid2>
        </Grid2>
      </Box>

      {/* Employee Details Section */}
      <Box>
        <Typography variant="body2" fontWeight={600} gutterBottom>
          Employee Details
        </Typography>
        <DataTable
          data={payrunDetails?.details || []}
          enableFilters
          enableGlobalFilterModes={false}
          columns={[
            {
              accessorKey: "employee.display_name",
              header: "Employee",
              size: 200,
              Cell: ({ row }) => (
                <Typography variant="body2" color="primary" sx={{ cursor: "pointer" }}>
                  {row?.original?.employee?.display_name || "-"}
                </Typography>
              ),
            },
            {
              accessorKey: "paid_days",
              header: "Paid Days",
              size: 120,
              Cell: ({ row }) => row?.original?.paid_days || "-",
            },
            {
              accessorKey: "gross_pay",
              header: "Gross Pay",
              size: 150,
              Cell: ({ row }) => <EffiMask isMasked={false}>{formatCurrency(row?.original?.gross_pay || 0)}</EffiMask>,
            },
            {
              accessorKey: "deductions",
              header: "Deductions",
              size: 150,
              Cell: ({ row }) => <EffiMask isMasked={false}>{formatCurrency(row?.original?.deductions || 0)}</EffiMask>,
            },
            {
              accessorKey: "tds",
              header: "TDS",
              size: 120,
              Cell: ({ row }) => <EffiMask isMasked={false}>{formatCurrency(row?.original?.tax_amount || 0)}</EffiMask>,
            },
            {
              accessorKey: "net_amount",
              header: "Net Amount",
              size: 150,
              Cell: ({ row }) => <EffiMask isMasked={false}>{formatCurrency(row?.original?.net_pay || 0)}</EffiMask>,
            },
            {
              accessorKey: "actions",
              header: "Actions",
              size: 100,
              enableSorting: false,
              enableColumnFilter: false,
              Cell: ({ row }) => (
                <Tooltip title="Download Employee Payslip">
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => downloadEmployeePayslipMutation.mutate(row?.original?.id)}
                    disabled={downloadEmployeePayslipMutation.isLoading || !row?.original?.id}
                    sx={{
                      backgroundColor: "primary.main",
                      color: "white",
                      "&:hover": {
                        backgroundColor: "primary.dark",
                      },
                      "&:disabled": {
                        backgroundColor: "grey.300",
                        color: "grey.500",
                      },
                    }}
                  >
                    <DownloadForOfflineOutlined fontSize="small" />
                  </IconButton>
                </Tooltip>
              ),
            },
          ]}
          state={{
            showSkeletons: isLoading,
            showColumnFilters: true,
          }}
        />
      </Box>
    </Box>
  );
};

export default PayrunHistoryDetail;
