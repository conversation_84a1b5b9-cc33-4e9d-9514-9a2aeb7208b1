import { DownloadForOfflineOutlined, InfoOutlined } from "@mui/icons-material";
import { Box, Tooltip, Typography } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React from "react";
import EffiMask from "src/modules/Common/EffiViews/components/EffiMask";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import payrollService from "src/services/payroll.service";
import { formatDateToDayMonthYear, getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";

const PayrunHistory = () => {
  // const [selectedRows, setSelectedRows] = useState({});
  const { data, isLoading, isFetched } = useQuery(["get-payrun-history"], async () =>
    payrollService.getPayrunHistory(),
  );

  const exportPayrunDetailsMutation = useMutation({
    mutationKey: ["export-payrun-details-history"],
    mutationFn: async (payload: { payrunType: string; payrunId: string }) =>
      payrollService.exportPayrunDetails(payload.payrunType, payload.payrunId),
  });
  if (!data) {
    return null;
  }
  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <DataTable
        data={data || []}
        enableBatchRowSelection
        // enableRowSelection
        // onRowSelectionChange={setSelectedRows}
        enableFilters
        enableGlobalFilterModes={false}
        enableRowActions
        positionActionsColumn="last"
        enableColumnFilters
        renderRowActions={({ row }) => (
          <Box display="flex" justifyItems="flex-end">
            <TableActions
              view={{
                onClick: () => {},
              }}
              edit={{ hide: true }}
              remove={{ hide: true }}
              renderCustomActions={() => [
                {
                  title: "Download",
                  Icon: DownloadForOfflineOutlined,
                  tooltip: "Download",
                  onClick: () =>
                    exportPayrunDetailsMutation.mutate({
                      payrunType: row.original.pay_run_type,
                      payrunId: row.original.pay_run_id,
                    }),
                },
              ]}
            />
          </Box>
        )}
        columns={[
          {
            accessorKey: "pay_run_type",
            header: "Pay Run Type",
          },
          {
            accessorKey: "period",
            header: "Period",
            enableColumnFilter: true,
          },
          {
            accessorKey: "total_net_pay",
            header: "Net Amount",
            Cell: ({ row }) => <EffiMask isMasked={false}>{formatCurrency(row?.original?.total_net_pay)}</EffiMask>,
            enableColumnFilter: false,
          },
          {
            accessorKey: "status",
            header: "Status",
            Cell: ({ row }) => (
              <Box display="flex" gap={0.5} alignItems={"center"}>
                <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>
                {row?.original?.comments && (
                  <Tooltip title={row?.original?.comments}>
                    <InfoOutlined fontSize="small" />
                  </Tooltip>
                )}
              </Box>
            ),
          },
          {
            accessorKey: "payment_date",
            header: "Payment Date",
            Cell: ({ row }) =>
              row?.original?.payment_date ? formatDateToDayMonthYear(row?.original?.payment_date) : "-",
            enableColumnFilter: false,
          },
          {
            accessorKey: "payment_mode",
            header: "Payment Mode",
            Cell: ({ row }) => row?.original?.payment_mode || "-",
          },
          {
            accessorKey: "processed_at",
            header: "Processed At",
            enableColumnFilter: false,
            size: 250,
            Cell: ({ row }) =>
              getIntlTimeToSpecifiedFormat(row?.original?.processed_at, "dd MMM yyyy HH:MM").formattedDate,
          },
        ]}
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 150,
            header: "",
          },
        }}
        state={{
          showSkeletons: isLoading || !isFetched,
          // rowSelection: selectedRows,
          columnPinning: {
            right: ["mrt-row-actions"],
          },
          showColumnFilters: true,
        }}
      />
    </Box>
  );
};

export default PayrunHistory;
