import { Block, Delete, InfoOutlined, Paid } from "@mui/icons-material";
import { Box, IconButton, Link, Stack, Tooltip, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { MRT_ColumnDef, MRT_RowSelectionState } from "material-react-table";
import React, { Dispatch, SetStateAction, useMemo, useState } from "react";
import { queryClient } from "src/app/App";
import { LogoutIcon } from "src/assets/icons.svg";
import { useAppDispatch } from "src/customHooks/useAppDispatch";

import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiMask from "src/modules/Common/EffiViews/components/EffiMask";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import ConfirmationModal from "src/modules/Common/Modal/ConfirmationModal";
import DataTable from "src/modules/Common/Table/DataTable";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import { PayRunDetail } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { getStatusColors } from "src/utils/typographyUtils";
import MarkAsPaidModal from "../Common/MarkAsPaidModal";
import AddBulkPayrun from "./AddBulkPayrun";
import { PayrunDetailsContext, PayrunDetailsViewModes } from "./PayrunDetails";

interface AllPayrunDetailsProps {
  payruns?: PayRunDetail[];
  setSelectedPayrun: Dispatch<SetStateAction<PayRunDetail | null>>;
  setPayrunDetailsViewModes: (mode: PayrunDetailsViewModes) => void;
}

const DeletePayrunModal = ({
  isDeletePayrunModalOpen,
  closeModal,
  deletePayrunId,
}: {
  isDeletePayrunModalOpen: boolean;
  closeModal: () => void;
  deletePayrunId: { id: string; payrunType: string } | null;
}) => {
  const { mutate: deletePayrun } = useMutation({
    mutationFn: (payload: { id: string; payrunType: string }) =>
      payrollService.deletePayRun(payload.id, payload.payrunType),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
      closeModal();
    },
  });
  return (
    <ConfirmationModal
      isOpen={isDeletePayrunModalOpen}
      onSubmit={() => deletePayrunId && deletePayrun(deletePayrunId)}
      onCancel={closeModal}
      title="Delete Pay Run"
    >
      <Typography>Are you sure you want to delete this pay run?</Typography>
    </ConfirmationModal>
  );
};

const AllPayrunDetails: React.FC<AllPayrunDetailsProps> = ({
  payruns,
  setSelectedPayrun,
  setPayrunDetailsViewModes,
}) => {
  const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>({});
  const [deletePayrunId, setDeletePayrunId] = useState<{ id: string; payrunType: string } | null>(null);
  const [payrunAsPaid, setPayrunAsPaid] = useState<PayRunDetail[]>([]);
  const [skipPayrunId, setSkipPayrunId] = useState<string[] | null>(null);
  const [confirmationModalType, setConfirmationModalType] = useState<"skip" | null>(null);
  const dispatch = useAppDispatch();
  const [isAddBulkPayrunModalOpen, setIsAddBulkPayrunModalOpen] = useState(false);
  const [submitPayrunId, setSubmitPayrunId] = useState<{ payrunIds: string[]; payrunType: string } | null>(null);
  const selectedRows = payruns?.filter((_eachPayrun, index) => rowSelection[index]);
  const { isMasked, isAdHocPayrunFlow } = React.useContext(PayrunDetailsContext);

  // Form for skip reason
  const skipForm = useAppForm({
    defaultValues: {
      reason: "",
    },
  });
  const { reason } = useStore(skipForm.store, (state) => state.values);

  const markAsPaidMutation = useMutation({
    mutationFn: async (payload: {
      payrunIds: string[];
      payment_mode: string;
      payment_date: string;
      pay_run_type: string;
    }) =>
      payrollService.markPayRunAsPaid(
        payload.payrunIds,
        payload.payment_mode,
        payload.payment_date,
        payload.pay_run_type,
      ),
    onSuccess: () => {
      // Refresh only relevant queries to avoid resetting parent pages/tabs
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
      setPayrunAsPaid([]);
      setSelectedPayrun(null);
    },
  });

  const onPayrunDetailClick = (row: any) => {
    setPayrunDetailsViewModes(PayrunDetailsViewModes.EDIT_PAYRUN_DETAIL);
    setSelectedPayrun(row);
    dispatch(setFullviewMode(true));
  };

  const submitPayrunMutation = useMutation({
    mutationFn: ({ payrunIds, payrunType }: { payrunIds: string[]; payrunType: string }) =>
      payrollService.submitPayrun(payrunIds, payrunType),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
      setSubmitPayrunId(null);
    },
  });

  const skipPayrunMutation = useMutation({
    mutationFn: (payload: { payrunIds: string[]; reason: string; payrunType: string }) =>
      payrollService.skipPayRun(payload.payrunIds, payload.reason, payload.payrunType),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
      setSkipPayrunId(null);
      setConfirmationModalType(null);
    },
  });

  const onPayrunSubmit = (ids: string[], payrunType: string) => {
    setSubmitPayrunId({
      payrunIds: ids,
      payrunType,
    });
  };

  const onPayrunAsPaidClick = (payruns: PayRunDetail[]) => {
    setPayrunAsPaid(payruns);
  };

  const onSkipPayrun = (payrunIds: string[]) => {
    setSkipPayrunId(payrunIds);
    setConfirmationModalType("skip");
  };

  const onBulkSkipPayrun = () => {
    if (skipPayrunId) {
      skipPayrunMutation.mutate({
        payrunIds: skipPayrunId,
        reason,
        payrunType: selectedRows?.map((payrun) => payrun?.pay_run_type)?.[0] || "",
      });
    }
  };

  const columns = useMemo((): MRT_ColumnDef<PayRunDetail>[] => {
    if (isAdHocPayrunFlow) {
      return [
        {
          accessorKey: "id",
          header: "Pay Run ID",
          minSize: 250,
          Cell: ({ row }) => (
            <Link
              underline="hover"
              onClick={() => onPayrunDetailClick(row?.original)}
              sx={{ cursor: "pointer", color: (theme) => theme.palette.text.link }}
            >
              {row?.original?.id}
            </Link>
          ),
        },
        {
          accessorKey: "pay_run_type",
          header: "Pay Run Type",
        },
        {
          accessorKey: "active_employees",
          header: "No. of Employees",
        },
        {
          accessorKey: "total_net_pay",
          header: "Net Pay",
          Cell: ({ row }) => <EffiMask isMasked={isMasked}>{formatCurrency(row?.original?.total_net_pay)}</EffiMask>,
        },
        {
          accessorKey: "status",
          header: "Status",
          size: 150,
          Cell: ({ row }) => (
            <Box display="flex" gap={0.5} alignItems={"center"}>
              <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>
              {row?.original?.comments && (
                <Tooltip title={row?.original?.comments}>
                  <InfoOutlined fontSize="small" />
                </Tooltip>
              )}
            </Box>
          ),
        },
      ];
    }
    return [
      {
        accessorKey: "id",
        header: "Pay Run ID",
        minSize: 250,
        Cell: ({ row }) => (
          <Link
            underline="hover"
            onClick={() => onPayrunDetailClick(row?.original)}
            sx={{ cursor: "pointer", color: (theme) => theme.palette.text.link }}
          >
            {row?.original?.id}
          </Link>
        ),
      },
      {
        accessorKey: "pay_schedule_name",
        header: "Schedule Name",
        minSize: 250,
      },
      {
        accessorKey: "period",
        header: "Payroll Period",
        size: 150,
      },
      {
        accessorKey: "active_employees",
        header: "No. of Employees",
        size: 200,
      },
      {
        accessorKey: "total_net_pay",
        header: "Net Pay",
        Cell: ({ row }) => <EffiMask isMasked={isMasked}>{formatCurrency(row?.original?.total_net_pay)}</EffiMask>,
        size: 150,
      },
      {
        accessorKey: "status",
        header: "Status",
        size: 150,
        Cell: ({ row }) => (
          <Box display="flex" gap={0.5} alignItems={"center"}>
            <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>
            {row?.original?.comments && (
              <Tooltip title={row?.original?.comments}>
                <InfoOutlined fontSize="small" />
              </Tooltip>
            )}
          </Box>
        ),
      },
    ];
  }, [isMasked, isAdHocPayrunFlow]);

  const bulkOptions = [
    {
      title: "Submit Payrun",
      onClick: () =>
        onPayrunSubmit(selectedRows?.map((payrun) => payrun.id) || [], selectedRows?.[0]?.pay_run_type || ""),
      id: "submit-payrun",
      icon: <LogoutIcon />,
      disabled:
        selectedRows?.length === 0 ||
        selectedRows?.some((payrun) => ["Submitted", "Paid", "Approved"].includes(payrun.status)) ||
        selectedRows?.some((payrun) => payrun.active_employees === 0),
    },
    {
      id: "mark-as-paid",
      title: "Mark as Paid",
      onClick: () => onPayrunAsPaidClick(selectedRows || []),
      icon: <Paid />,
      disabled: selectedRows?.length === 0 || !selectedRows?.every((payrun) => payrun.status === "Approved"),
    },
    ...(selectedRows?.some((payrun) => payrun.active_employees === 0)
      ? [
          {
            title: "Skip Payrun",
            icon: <Block />,
            id: "skip-payrun",
            disabled: selectedRows?.length === 0 || selectedRows?.some((payrun) => payrun.active_employees !== 0),
            onClick: () =>
              onSkipPayrun(
                selectedRows?.filter((payrun) => payrun.active_employees === 0).map((payrun) => payrun.id) || [],
              ),
          },
        ]
      : []),
  ];

  return (
    <Box id="payrun-details" display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title="Payrun Details"
        actions={
          <Stack direction="row" gap={1}>
            {bulkOptions.map((eachOption) => (
              <Box key={eachOption.id} sx={{ bgcolor: eachOption.disabled ? "#D2D2D2" : "#E6F2F1", borderRadius: 8 }}>
                <Tooltip title={eachOption.title}>
                  <IconButton
                    key={eachOption.id}
                    size="medium"
                    onClick={eachOption.onClick}
                    disabled={eachOption.disabled}
                  >
                    {eachOption.icon}
                  </IconButton>
                </Tooltip>
              </Box>
            ))}
          </Stack>
        }
      />
      <DataTable
        data={payruns || []}
        enableBatchRowSelection
        enableRowSelection
        onRowSelectionChange={setRowSelection}
        enableRowActions
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 200,
            maxSize: 200,
            header: "",
            muiTableBodyCellProps: {
              align: "right",
            },
            enablePinning: true,
          },
        }}
        renderRowActions={({ row }) => (
          <Box display="flex" flexDirection="row" gap={1}>
            {!["Submitted", "Paid", "Approved"].includes(row?.original?.status) && (
              <>
                {row?.original?.active_employees > 0 && (
                  <Tooltip title="Submit">
                    <IconButton
                      color="primary"
                      onClick={() => onPayrunSubmit([row?.original?.id], row?.original?.pay_run_type)}
                    >
                      <LogoutIcon />
                    </IconButton>
                  </Tooltip>
                )}
                {row?.original?.active_employees === 0 && (
                  <Tooltip title="Skip Payrun">
                    <IconButton color="primary" size="small" onClick={() => onSkipPayrun([row?.original?.id])}>
                      <Block />
                    </IconButton>
                  </Tooltip>
                )}
                <Tooltip title="Delete Pay Run">
                  <IconButton
                    color="error"
                    size="small"
                    onClick={() =>
                      setDeletePayrunId({ id: row?.original?.id, payrunType: row?.original?.pay_run_type })
                    }
                  >
                    <Delete />
                  </IconButton>
                </Tooltip>
              </>
            )}
            {row.original.status === "Approved" && (
              <Tooltip title="Mark as Paid">
                <IconButton color="primary" size="small" onClick={() => onPayrunAsPaidClick([row?.original])}>
                  <Paid />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        )}
        state={{
          rowSelection: rowSelection,
          columnPinning: {
            right: ["mrt-row-actions"],
          },
        }}
        layoutMode="grid"
        columns={columns}
      />
      {isAddBulkPayrunModalOpen && selectedRows && (
        <AddBulkPayrun
          isModalOpen={isAddBulkPayrunModalOpen}
          onClose={() => setIsAddBulkPayrunModalOpen(false)}
          selectedRows={selectedRows}
        />
      )}
      <DeletePayrunModal
        isDeletePayrunModalOpen={!!deletePayrunId}
        closeModal={() => setDeletePayrunId(null)}
        deletePayrunId={deletePayrunId}
      />
      {payrunAsPaid.length > 0 && (
        <MarkAsPaidModal
          isOpen={payrunAsPaid.length > 0}
          onClose={() => {
            setPayrunAsPaid([]);
            setSelectedPayrun(null);
            setRowSelection({});
          }}
          onMarkAsPaid={({ value }) => {
            markAsPaidMutation.mutate({
              payrunIds: payrunAsPaid.map((payrun) => payrun.id),
              payment_mode: value.paymentType,
              payment_date: value.paymentDate,
              pay_run_type: payrunAsPaid[0]?.pay_run_type || "",
            });
          }}
          selectedPayruns={payrunAsPaid.map((payrun) => payrun.id)}
        />
      )}
      {submitPayrunId && (
        <DeleteConfirmationModal
          onCancel={() => setSubmitPayrunId(null)}
          onDelete={() => submitPayrunMutation.mutate(submitPayrunId)}
          isModalOpen={!!submitPayrunId}
          selectedRole={submitPayrunId?.payrunIds?.join(", ") || ""}
          title="Are you sure you want to submit this pay run?"
          suffix="submitted"
          saveButtonTitle="Submit"
        />
      )}
      {confirmationModalType === "skip" && (
        <ConfirmationModal
          isOpen={confirmationModalType === "skip"}
          title="Skip Pay Run"
          onSubmit={() => onBulkSkipPayrun()}
          onCancel={() => {
            setConfirmationModalType(null);
            setSkipPayrunId(null);
          }}
          isSaveDisabled={!reason}
        >
          <Box display="flex" flexDirection="column" gap={2}>
            <Typography>Are you sure you want to skip this pay run? Please provide a reason.</Typography>
            <skipForm.AppField name="reason">
              {(field: any) => <field.EffiTextField label="Reason" required />}
            </skipForm.AppField>
          </Box>
        </ConfirmationModal>
      )}
    </Box>
  );
};

export default AllPayrunDetails;
